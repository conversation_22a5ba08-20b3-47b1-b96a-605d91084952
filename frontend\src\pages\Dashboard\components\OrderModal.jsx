import React, { useState } from 'react';
import axios from 'axios';
import { X } from 'lucide-react';

// A comprehensive list of Algerian wilayas for the dropdown.
const algerianWilayas = [
  "1. <PERSON><PERSON>",
  "2. <PERSON><PERSON><PERSON>",
  "3. <PERSON><PERSON><PERSON><PERSON>",
  "4. <PERSON><PERSON>",
  "5. <PERSON><PERSON>",
  "6. <PERSON><PERSON>",
  "7. <PERSON><PERSON><PERSON>",
  "8. <PERSON><PERSON><PERSON><PERSON>",
  "9. <PERSON><PERSON><PERSON>",
  "10. <PERSON><PERSON><PERSON>",
  "11. <PERSON><PERSON><PERSON>",
  "12. <PERSON><PERSON><PERSON><PERSON>",
  "13. Tlemcen",
  "14. <PERSON><PERSON><PERSON>",
  "15. <PERSON><PERSON><PERSON>",
  "16. <PERSON>",
  "17. <PERSON><PERSON><PERSON><PERSON>",
  "18. <PERSON><PERSON><PERSON>",
  "19. <PERSON><PERSON><PERSON><PERSON>",
  "20. <PERSON><PERSON>",
  "21. <PERSON><PERSON><PERSON>",
  "22. <PERSON><PERSON>",
  "23. <PERSON><PERSON>",
  "24. <PERSON><PERSON><PERSON>",
  "25. <PERSON>",
  "26. <PERSON>",
  "27. <PERSON>",
  "28. <PERSON><PERSON><PERSON><PERSON>",
  "29. <PERSON><PERSON><PERSON>",
  "30. <PERSON><PERSON><PERSON><PERSON>",
  "31. <PERSON><PERSON>",
  "32. <PERSON>",
  "33. <PERSON><PERSON><PERSON>",
  "34. <PERSON><PERSON><PERSON>",
  "35. <PERSON><PERSON><PERSON><PERSON>",
  "36. <PERSON>",
  "37. <PERSON>",
  "38. <PERSON><PERSON><PERSON><PERSON>",
  "39. <PERSON><PERSON>",
  "40. <PERSON><PERSON><PERSON><PERSON><PERSON>",
  "41. <PERSON><PERSON><PERSON>",
  "42. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>",
  "43. <PERSON><PERSON><PERSON><PERSON><PERSON>",
  "44. <PERSON><PERSON><PERSON><PERSON>",
  "45. <PERSON> <PERSON>'<PERSON><PERSON><PERSON>",
  "46. <PERSON> <PERSON>",
  "47. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>",
  "48. <PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>",
  "49. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>",
  "50. <PERSON><PERSON>un",
  "51. In Salah",
  "52. In Guezzam",
  "53. Touggourt",
  "54. Djanet",
  "55. Ain Salah",
];

const OrderModal = ({ product, isOpen, onClose }) => {
  const initialState = {
    firstName: '',
    lastName: '',
    wilaya: '',
    address: '',
    phone1: '',
    phone2: '',
  };

  const [formData, setFormData] = useState(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // A single handler to update the form state based on input name
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    setSuccess(false);

    // Construct the payload for the backend API
    const orderPayload = {
      customerInfo: { ...formData },
      productId: product._id,
      productName: product.name,
      productImage: (product.images && product.images.length > 0) ? product.images[0] : null,
      totalPrice: product.price,
    };

    try {
      // Send the POST request to the create order endpoint
      await axios.post('http://localhost:5000/api/orders', orderPayload);
      
      setSuccess(true);
      setFormData(initialState); // Clear the form on success

      // Automatically close the modal after showing the success message
      setTimeout(() => {
        onClose();
        setSuccess(false); // Reset for the next time the modal is opened
      }, 2500);

    } catch (err) {
      // Display a user-friendly error message from the server response or a generic one
      setError(err.response?.data?.message || 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Do not render anything if the modal is not open
  if (!isOpen) return null;

  return (
    // Modal Overlay
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in">
      {/* Modal Content */}
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg relative max-h-[90vh] overflow-y-auto">
        
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white border-b p-5 flex justify-between items-center z-10">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-800">
              Order for <span className="text-indigo-600">{product.name}</span>
            </h2>
            <button onClick={onClose} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
                <X size={24} className="text-gray-600" />
            </button>
        </div>

        {/* Conditional Content: Success Message or Form */}
        {success ? (
          <div className="p-10 text-center">
            <h3 className="text-2xl font-bold text-green-600 mb-2">Order Placed Successfully!</h3>
            <p className="text-gray-600">Thank you. You will be contacted shortly for confirmation.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {error && <div className="p-3 bg-red-100 text-red-800 rounded-lg text-sm">{error}</div>}
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">First Name</label>
                <input type="text" name="firstName" value={formData.firstName} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Last Name</label>
                <input type="text" name="lastName" value={formData.lastName} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required />
              </div>
            </div>
            
            <div>
                <label className="block text-sm font-medium mb-1">Wilaya</label>
                <select name="wilaya" value={formData.wilaya} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option value="" disabled>-- Select a Wilaya --</option>
                    {algerianWilayas.map(w => <option key={w} value={w}>{w}</option>)}
                </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Full Address</label>
              <textarea name="address" value={formData.address} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg min-h-[80px] focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="e.g., Street Name, City, Postal Code" required />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Phone Number</label>
                <input type="tel" name="phone1" value={formData.phone1} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Optional Phone</label>
                <input type="tel" name="phone2" value={formData.phone2} onChange={handleFormChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" />
              </div>
            </div>
            
            <div className="pt-4 flex justify-end">
              <button type="submit" disabled={loading} className="w-full sm:w-auto px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-all duration-300 disabled:bg-indigo-300 disabled:cursor-not-allowed">
                {loading ? 'Processing...' : `Order Now for $${product.price.toFixed(2)}`}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default OrderModal;