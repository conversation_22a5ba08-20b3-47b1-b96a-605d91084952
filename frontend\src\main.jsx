import React from 'react';
import ReactDOM from 'react-dom/client';
import axios from 'axios';

import App from './App';
import { AuthProvider } from './context/AuthContext';
import './index.css';

// --- Axios Global Configuration ---
// This sets a base URL for all Axios requests.
// Now, instead of axios.post('http://localhost:5001/api/auth/login'),
// you can just write axios.post('/api/auth/login').
// Ensure your backend runs on port 5001 or change this URL.
axios.defaults.baseURL = 'http://localhost:5001';
axios.defaults.headers.common['Content-Type'] = 'application/json';


const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    {/*
      AuthProvider wraps the entire application, making authentication state
      globally available to all components and pages.
    */}
    <AuthProvider>
      <App />
    </AuthProvider>
  </React.StrictMode>
);