import React from 'react';
import { NavLink, Link, Outlet } from 'react-router-dom';
import { LayoutDashboard,  Plus, Images, Link as LinkIcon, ExternalLink } from 'lucide-react';
import {BiListCheck, BiCategoryAlt} from 'react-icons/bi'
import { FaBoxOpen } from 'react-icons/fa'

// Sidebar link data
const sidebarLinks = [
  { to: '/dashboard', icon: <LayoutDashboard size={18} />, text: 'Overview' },
  { to: '/dashboard/products', icon: <FaBoxOpen size={18} />, text: 'Crate Products' },
  { to: '/dashboard/slider', icon: <Images size={18} />, text: 'Manage Slider' },
  { to: '/dashboard/navbar', icon: <LinkIcon size={18} />, text: 'Manage Navbar' }, 
  { to: '/dashboard/orders', icon: <BiListCheck size={18} />, text: 'Orders' }, 
  { to: '/dashboard/categories', icon: <BiCategoryAlt size={18} />, text: 'Catagories' }, 
];

// Reusable SidebarLink component
const SidebarLink = ({ to, icon, text }) => {
  const activeClassName = "bg-indigo-600 text-white";
  const inactiveClassName = "text-gray-300 hover:bg-gray-700 hover:text-white";

  return (
    <li>
      <NavLink
        to={to}
        end // Use 'end' for exact matching of the parent route
        className={({ isActive }) =>
          `flex items-center space-x-3 py-2.5 px-4 rounded-md mx-2 transition-colors duration-200 ${
            isActive ? activeClassName : inactiveClassName
          }`
        }
      >
        {icon}
        <span className="font-medium">{text}</span>
      </NavLink>
    </li>
  );
};


const Sidebar = () => {
  return (
    <div className="w-64 flex-shrink-0 bg-gray-800 text-white flex flex-col">
      <div className="h-16 flex items-center justify-center text-xl font-bold border-b border-gray-700">
        <Link to="/dashboard">Dashboard</Link>
      </div>
      <nav className="mt-6 flex-1">
        <ul className="space-y-2">
          {sidebarLinks.map(link => (
            <SidebarLink key={link.to} {...link} />
          ))}
        </ul>
      </nav>
      {/* Footer link to view public site */}
      <div className="mt-auto border-t border-gray-700 p-2">
         <a 
            href="/products" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="flex items-center space-x-3 py-2.5 px-4 rounded-md mx-2 transition-colors duration-200 text-gray-400 hover:bg-gray-700 hover:text-white"
        >
            <ExternalLink size={18} />
            <span className="font-medium">View Public Site</span>
         </a>
      </div>
    </div>
  );
};

// Layout component that includes the sidebar and content area
const DashboardLayout = () => {
  return (
    <div className="flex h-screen bg-gray-100 font-sans">
      <Sidebar />
      <main className="flex-1 overflow-y-auto">
        {/* The <Outlet> component will render the matched child route component (e.g., DashboardHomePage, ProductManagementPage, etc.) */}
        <Outlet />
      </main>
    </div>
  );
};

export default DashboardLayout;