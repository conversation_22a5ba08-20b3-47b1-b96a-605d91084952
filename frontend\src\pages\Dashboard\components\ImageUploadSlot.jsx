import React from 'react';
import { Image, UploadCloud, X } from 'lucide-react';

const ImageUploadSlot = ({ label, preview, onImageChange, onClear, id, isRequired }) => {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {isRequired && <span className="text-red-500">*</span>}
      </label>
      <div className="relative group w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-400 hover:border-indigo-500 transition-colors">
        {preview ? (
          <>
            <img src={preview} alt="Preview" className="w-full h-full object-cover rounded-lg" />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
              <button
                type="button"
                onClick={onClear}
                className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700"
                aria-label="Remove image"
              >
                <X size={16} />
              </button>
            </div>
          </>
        ) : (
          <div className="text-center">
            <UploadCloud size={32} />
            <p className="mt-1 text-xs">Click to upload</p>
          </div>
        )}
        <input
          type="file"
          id={id}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onChange={onImageChange}
          accept="image/png, image/jpeg"
        />
      </div>
    </div>
  );
};

export default ImageUploadSlot;