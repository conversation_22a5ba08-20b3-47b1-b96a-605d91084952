import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Navbar from '../common/navbar/Navbar'; // Your path should be correct
import Footer from '../common/footer/Footer';

const MainLayout = () => {
  const location = useLocation();
  
  // Check if current path is dashboard or 404
  const shouldHideFooter = 
    location.pathname.includes('/dashboard') || 
    location.pathname.includes('/404') ||
    location.pathname.includes('/not-found');

  return (
    <>
      <Navbar />
      {/* The main content of the page will be rendered here, below the navbar */}
      {/* The pt-16 class is crucial. It adds padding to the top of the main content, 
          equal to the height of the fixed navbar (h-16 in Tailwind = 4rem = 64px),
          preventing your page content from being hidden underneath it. */}
      <main className="pt-16 min-h-screen"> 
        <Outlet />
      </main>
      {!shouldHideFooter && <Footer />}
    </>
  );
};

export default MainLayout;