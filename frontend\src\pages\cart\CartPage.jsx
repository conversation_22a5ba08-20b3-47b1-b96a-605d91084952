import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import { Link } from 'react-router-dom';
import { Trash2, Plus, Minus, ShoppingCart, ArrowLeft, Heart, Gift, Shield, Truck, Tag } from 'lucide-react';

const CartPage = () => {
  const { cartItems, removeFromCart, updateQuantity, cartCount } = useCart();
  const [removingItem, setRemovingItem] = useState(null);
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState(null);

  const subtotal = cartItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  const shipping = subtotal > 75 ? 0 : 9.99;
  const discount = appliedPromo ? subtotal * 0.1 : 0;
  const tax = (subtotal - discount) * 0.08;
  const total = subtotal + shipping + tax - discount;

  const handleRemoveItem = async (itemId) => {
    setRemovingItem(itemId);
    setTimeout(() => {
      removeFromCart(itemId);
      setRemovingItem(null);
    }, 300);
  };

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      handleRemoveItem(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleApplyPromo = () => {
    if (promoCode.toLowerCase() === 'save10') {
      setAppliedPromo({ code: 'SAVE10', discount: 0.1 });
    }
  };

  const EmptyCart = () => (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8">
          <ShoppingCart size={64} className="text-gray-400" />
        </div>
        <h2 className="text-3xl font-bold text-gray-800 mb-4">Your cart is empty</h2>
        <p className="text-gray-600 mb-8 text-lg">
          Discover amazing products and start building your perfect collection.
        </p>
        <Link
          to="/products"
          className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          <ArrowLeft size={20} />
          Continue Shopping
        </Link>
      </div>
    </div>
  );

  if (cartItems.length === 0) {
    return <EmptyCart />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                to="/products"
                className="flex items-center gap-2 text-indigo-600 hover:text-indigo-700 font-medium transition-colors"
              >
                <ArrowLeft size={20} />
                Continue Shopping
              </Link>
            </div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Shopping Cart</h1>
              <span className="bg-indigo-100 text-indigo-700 font-semibold rounded-full px-3 py-1">
                {cartCount} {cartCount === 1 ? 'item' : 'items'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {/* Free Shipping Banner */}
            {subtotal < 75 && (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-3">
                  <Truck className="text-green-600" size={24} />
                  <div>
                    <p className="font-semibold text-green-800">
                      Add ${(75 - subtotal).toFixed(2)} more for FREE shipping!
                    </p>
                    <p className="text-sm text-green-600">
                      You're almost there! Free shipping on orders over $75.
                    </p>
                  </div>
                </div>
                <div className="mt-3 bg-green-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min((subtotal / 75) * 100, 100)}%` }}
                  />
                </div>
              </div>
            )}

            {/* Cart Items List */}
            <div className="bg-white rounded-xl shadow-sm border">
              {cartItems.map((item, index) => {
                const mainImage = (item.images && item.images.length > 0)
                  ? item.images[0]
                  : item.imageData || 'https://via.placeholder.com/150?text=No+Image';

                return (
                  <div
                    key={item._id}
                    className={`p-6 transition-all duration-300 ${
                      removingItem === item._id ? 'opacity-50 scale-95' : ''
                    } ${index !== cartItems.length - 1 ? 'border-b' : ''}`}
                  >
                    <div className="flex flex-col sm:flex-row gap-4">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <img 
                          src={mainImage}
                          alt={item.name}
                          className="w-full sm:w-32 h-32 rounded-lg object-cover bg-gray-100"
                        />
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              {item.name}
                            </h3>
                            <p className="text-sm text-gray-500 mb-3">
                              Unit Price: ${item.price.toFixed(2)}
                            </p>
                            
                            {/* Mobile Actions */}
                            <div className="flex items-center justify-between sm:hidden">
                              <div className="flex items-center gap-3">
                                <div className="flex items-center border border-gray-300 rounded-lg">
                                  <button
                                    onClick={() => handleQuantityChange(item._id, item.quantity - 1)}
                                    className="p-2 hover:bg-gray-100 rounded-l-lg transition-colors"
                                  >
                                    <Minus size={16} />
                                  </button>
                                  <span className="px-4 py-2 font-semibold min-w-[3rem] text-center">
                                    {item.quantity}
                                  </span>
                                  <button
                                    onClick={() => handleQuantityChange(item._id, item.quantity + 1)}
                                    className="p-2 hover:bg-gray-100 rounded-r-lg transition-colors"
                                  >
                                    <Plus size={16} />
                                  </button>
                                </div>
                                <button
                                  onClick={() => handleRemoveItem(item._id)}
                                  className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                                >
                                  <Trash2 size={20} />
                                </button>
                              </div>
                              <div className="text-right">
                                <p className="text-xl font-bold text-indigo-600">
                                  ${(item.price * item.quantity).toFixed(2)}
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Desktop Actions */}
                          <div className="hidden sm:flex flex-col items-end gap-4">
                            <div className="text-right">
                              <p className="text-xl font-bold text-indigo-600">
                                ${(item.price * item.quantity).toFixed(2)}
                              </p>
                            </div>
                            <div className="flex items-center gap-3">
                              <div className="flex items-center border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => handleQuantityChange(item._id, item.quantity - 1)}
                                  className="p-2 hover:bg-gray-100 rounded-l-lg transition-colors"
                                >
                                  <Minus size={16} />
                                </button>
                                <span className="px-4 py-2 font-semibold min-w-[3rem] text-center">
                                  {item.quantity}
                                </span>
                                <button
                                  onClick={() => handleQuantityChange(item._id, item.quantity + 1)}
                                  className="p-2 hover:bg-gray-100 rounded-r-lg transition-colors"
                                >
                                  <Plus size={16} />
                                </button>
                              </div>
                              <button
                                onClick={() => handleRemoveItem(item._id)}
                                className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                              >
                                <Trash2 size={20} />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Saved for Later / Wishlist */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Heart size={20} />
                You might also like
              </h3>
              <p className="text-gray-600">
                Continue shopping to discover more amazing products that match your style.
              </p>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Summary Card */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-6">Order Summary</h2>
                
                <div className="space-y-4">
                  <div className="flex justify-between text-gray-600">
                    <span>Subtotal ({cartCount} items)</span>
                    <span className="font-semibold">${subtotal.toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between text-gray-600">
                    <span>Shipping</span>
                    <span className="font-semibold">
                      {shipping === 0 ? (
                        <span className="text-green-600">Free</span>
                      ) : (
                        `$${shipping.toFixed(2)}`
                      )}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-gray-600">
                    <span>Tax</span>
                    <span className="font-semibold">${tax.toFixed(2)}</span>
                  </div>
                  
                  {appliedPromo && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount ({appliedPromo.code})</span>
                      <span className="font-semibold">-${discount.toFixed(2)}</span>
                    </div>
                  )}
                  
                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-gray-900">Total</span>
                      <span className="text-2xl font-bold text-gray-900">${total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Promo Code */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Tag size={16} className="text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Promo Code</span>
                  </div>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      placeholder="Enter code"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    />
                    <button
                      onClick={handleApplyPromo}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm hover:bg-gray-700 transition-colors"
                    >
                      Apply
                    </button>
                  </div>
                  {appliedPromo && (
                    <p className="text-sm text-green-600 mt-2">✓ Promo code applied!</p>
                  )}
                </div>

                {/* Checkout Button */}
                <button className="w-full mt-6 py-4 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  Proceed to Checkout
                </button>
              </div>

              {/* Security Features */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Why shop with us?</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="text-green-600" size={20} />
                    <span className="text-sm text-gray-600">Secure checkout</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Truck className="text-green-600" size={20} />
                    <span className="text-sm text-gray-600">Free shipping over $75</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Gift className="text-green-600" size={20} />
                    <span className="text-sm text-gray-600">Easy returns</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;