import React from 'react';
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaFacebookF, FaTwitter, FaInstagram, FaLinkedinIn } from 'react-icons/fa';
import { motion } from 'framer-motion';

const ContactInfo = () => {
  return (
    <div className="bg-gradient-to-br from-purple-900 to-indigo-800 text-white p-10">
      <motion.h2 
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="text-3xl font-bold mb-8"
      >
        Contact Information
      </motion.h2>
      
      <motion.div 
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-8"
      >
        <div className="flex items-start">
          <div className="bg-purple-500/20 p-3 rounded-lg mr-4">
            <FaMapMarkerAlt className="text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">Our Location</h3>
            <p className="text-gray-300">123 Innovation Street</p>
            <p className="text-gray-300">Tech District, San Francisco</p>
            <p className="text-gray-300">CA 94103, United States</p>
          </div>
        </div>
        
        <div className="flex items-start">
          <div className="bg-purple-500/20 p-3 rounded-lg mr-4">
            <FaPhone className="text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">Phone & Fax</h3>
            <p className="text-gray-300">+****************</p>
            <p className="text-gray-300">+****************</p>
          </div>
        </div>
        
        <div className="flex items-start">
          <div className="bg-purple-500/20 p-3 rounded-lg mr-4">
            <FaEnvelope className="text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">Email Address</h3>
            <p className="text-gray-300"><EMAIL></p>
            <p className="text-gray-300"><EMAIL></p>
          </div>
        </div>
        
        <div className="flex items-start">
          <div className="bg-purple-500/20 p-3 rounded-lg mr-4">
            <FaClock className="text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">Business Hours</h3>
            <p className="text-gray-300">Monday - Friday: 9am - 6pm</p>
            <p className="text-gray-300">Saturday: 10am - 4pm</p>
            <p className="text-gray-300">Sunday: Closed</p>
          </div>
        </div>
      </motion.div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="mt-12"
      >
        <h3 className="text-xl font-semibold mb-4">Follow Us</h3>
        <div className="flex space-x-4">
          <a href="#" className="bg-white/10 p-3 rounded-full hover:bg-white/20 transition-colors">
            <FaFacebookF />
          </a>
          <a href="#" className="bg-white/10 p-3 rounded-full hover:bg-white/20 transition-colors">
            <FaTwitter />
          </a>
          <a href="#" className="bg-white/10 p-3 rounded-full hover:bg-white/20 transition-colors">
            <FaInstagram />
          </a>
          <a href="#" className="bg-white/10 p-3 rounded-full hover:bg-white/20 transition-colors">
            <FaLinkedinIn />
          </a>
        </div>
      </motion.div>
    </div>
  );
};

export default ContactInfo;
