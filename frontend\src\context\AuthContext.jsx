import React, { createContext, useState, useEffect, useContext } from 'react';

// 1. Create the context
export const AuthContext = createContext();

// Custom hook to use the AuthContext
export const useAuth = () => {
  return useContext(AuthContext);
};

// 2. Create the provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true); // To handle initial auth state check

  // Effect to check for existing user info in localStorage on app start
  useEffect(() => {
    try {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        const userInfo = JSON.parse(storedUserInfo);
        setUser(userInfo);
        setToken(userInfo.token);
      }
    } catch (error) {
      console.error("Failed to parse user info from localStorage", error);
      // If parsing fails, ensure state is clean
      setUser(null);
      setToken(null);
      localStorage.removeItem('userInfo');
    } finally {
      setLoading(false); // Stop loading once check is complete
    }
  }, []);

  // Login function: stores user info in state and localStorage
  const login = (userData) => {
    setUser(userData);
    setToken(userData.token);
    localStorage.setItem('userInfo', JSON.stringify(userData));
  };

  // Logout function: clears user info from state and localStorage
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('userInfo');
  };

  // The value provided to consuming components
  const value = {
    user,
    token,
    isLoggedIn: !!token, // Convenience boolean
    loading,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};