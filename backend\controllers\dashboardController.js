import Order from '../models/Order.js';
import Product from '../models/Product.js';

// @desc    Get aggregated dashboard statistics
// @route   GET /api/dashboard/stats
// @access  Private/Admin
export const getDashboardStats = async (req, res) => {
    try {
        const totalProducts = await Product.countDocuments();
        const pendingOrders = await Order.countDocuments({ status: 'Under Process' });

        // Calculate total revenue from only 'Confirmed' orders
        const confirmedOrders = await Order.find({ status: 'Confirmed' });
        const totalRevenue = confirmedOrders.reduce((acc, order) => acc + order.totalPrice, 0);

        res.json({
            totalRevenue,
            pendingOrders,
            totalProducts,
        });

    } catch (error) {
        res.status(500).json({ message: 'Server Error fetching dashboard stats', error: error.message });
    }
};