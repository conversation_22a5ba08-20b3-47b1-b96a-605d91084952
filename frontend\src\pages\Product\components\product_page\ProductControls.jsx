import React from 'react';
import { Search, ChevronDown, Filter } from 'lucide-react';

const ProductControls = ({
  searchTerm,
  onSearchChange,
  sortOrder,
  onSortChange,
  onFilterToggle,
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 items-center mb-8">
      {/* Search Input */}
      <div className="relative w-full md:flex-1">
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full outline-none pl-12 pr-4 py-3 border border-gray-300 rounded-full bg-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
        />
      </div>

      <div className="flex gap-4 w-full md:w-auto">
        {/* Mobile Filter Button */}
        <button
          onClick={onFilterToggle}
          className="md:hidden flex-1 flex items-center justify-center gap-2 py-3 px-4 bg-white border border-gray-300 rounded-full font-semibold text-gray-700"
        >
          <Filter size={16} /> Filters
        </button>

        {/* Sort Dropdown */}
        <div className="relative flex-1 md:flex-none">
          <select
            value={sortOrder}
            onChange={(e) => onSortChange(e.target.value)}
            className="w-full appearance-none bg-white border outline-none border-gray-300 rounded-full font-semibold text-gray-700 py-3 pl-4 pr-10 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="default">Default Sort</option>
            <option value="price-asc">Price: Low to High</option>
            <option value="price-desc">Price: High to Low</option>
            <option value="name-asc">Name: A-Z</option>
            <option value="name-desc">Name: Z-A</option>
          </select>
          <ChevronDown className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" size={20} />
        </div>
      </div>
    </div>
  );
};

export default ProductControls;