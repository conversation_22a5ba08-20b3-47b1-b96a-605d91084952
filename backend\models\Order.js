import mongoose from 'mongoose';

const orderSchema = new mongoose.Schema({
    product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true,
    },
    productName: { type: String, required: true },
    productImage: { type: String },
    totalPrice: { type: Number, required: true },
    customerInfo: {
        firstName: { type: String, required: true },
        lastName: { type: String, required: true },
        wilaya: { type: String, required: true },
        address: { type: String, required: true },
        phone1: { type: String, required: true },
        phone2: { type: String },
    },
    status: {
        type: String,
        required: true,
        enum: ['Under Process', 'Confirmed', 'Cancelled'],
        default: 'Under Process',
    },
}, { timestamps: true });

const Order = mongoose.model('Order', orderSchema);
export default Order;