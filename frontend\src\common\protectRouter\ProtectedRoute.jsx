import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';

const ProtectedRoute = () => {
  // Check if user info with a token exists in local storage
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));

  // If userInfo exists and has a token, allow access to the nested routes (Outlet).
  // Otherwise, redirect to the login page.
  return userInfo && userInfo.token ? <Outlet /> : <Navigate to="/login" replace />;
};

export default ProtectedRoute;