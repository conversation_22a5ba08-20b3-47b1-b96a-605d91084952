import React, { useState, useEffect } from 'react';

const UpdateModal = ({ product, isOpen, onClose, onUpdate }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [isNewProduct, setIsNewProduct] = useState(false);
  const [newImageData, setNewImageData] = useState(null); // Holds new Base64 data only
  const [preview, setPreview] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const API_URL = 'http://localhost:5000';

  // Pre-fill form when a product is selected
  useEffect(() => {
    if (product) {
      setName(product.name || '');
      setDescription(product.description || '');
      setPrice(product.price?.toString() || '');
      setIsNewProduct(product.isNewProduct || false);
      setPreview(product.imageData || ''); // Show existing image
      setNewImageData(null); // Reset new image state
      setError('');
    }
  }, [product]);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        setNewImageData(reader.result); // Store the new Base64 string
        setPreview(reader.result);      // Update preview to show new image
      };
      reader.onerror = (error) => {
        console.error("Error reading file:", error);
        setError("Failed to read the image file.");
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const updatedProductPayload = {
      name,
      description,
      price: parseFloat(price),
      isNewProduct,
    };

    // IMPORTANT: Only include the imageData field in the payload
    // if a new image was actually selected.
    if (newImageData) {
      updatedProductPayload.imageData = newImageData;
    }

    try {
      const response = await fetch(`${API_URL}/api/products/${product._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedProductPayload),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || 'Failed to update product');
      }
      
      const updatedProduct = await response.json();
      onUpdate(updatedProduct); // Update state in the parent component
      onClose(); // Close the modal

    } catch (err) {
      console.error('Error updating product:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">Update Product</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
          
          {error && <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg">{error}</div>}

          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2"><label className="block text-sm font-medium text-gray-700 mb-2">Product Image</label>
              <div className="flex items-center space-x-6">
                {preview ? <img src={preview} alt="Preview" className="w-24 h-24 rounded-lg object-cover border"/> : <div className="bg-gray-200 border-2 border-dashed rounded-xl w-24 h-24 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg></div>}
                <div>
                  <input type="file" id="image-upload-update" className="sr-only" onChange={handleImageChange} accept="image/png, image/jpeg"/>
                  <label htmlFor="image-upload-update" className="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer">Change Image</label>
                  <p className="mt-1 text-xs text-gray-500">JPG, PNG. (Max 10-12MB recommended)</p>
                </div>
              </div>
            </div>
            <div><label className="block text-sm font-medium text-gray-700 mb-2">Product Name</label><input type="text" className="w-full px-4 py-2 border border-gray-300 rounded-lg" value={name} onChange={(e) => setName(e.target.value)} required /></div>
            <div><label className="block text-sm font-medium text-gray-700 mb-2">Price ($)</label><input type="number" step="0.01" min="0" className="w-full px-4 py-2 border border-gray-300 rounded-lg" value={price} onChange={(e) => setPrice(e.target.value)} required /></div>
            <div className="md:col-span-2"><label className="block text-sm font-medium text-gray-700 mb-2">Description</label><textarea className="w-full px-4 py-2 border border-gray-300 rounded-lg min-h-[120px]" value={description} onChange={(e) => setDescription(e.target.value)} required /></div>
            <div><div className="flex items-center"><input type="checkbox" className="h-5 w-5 text-indigo-600 rounded" checked={isNewProduct} onChange={(e) => setIsNewProduct(e.target.checked)} /><label className="ml-2 text-sm font-medium text-gray-700">Mark as New Arrival</label></div></div>
            <div className="md:col-span-2 flex justify-end space-x-3 pt-4">
              <button type="button" onClick={onClose} className="px-6 py-2 border rounded-lg" disabled={loading}>Cancel</button>
              <button type="submit" className="px-6 py-2 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 flex items-center" disabled={loading}>{loading ? 'Updating...' : 'Update Product'}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UpdateModal;