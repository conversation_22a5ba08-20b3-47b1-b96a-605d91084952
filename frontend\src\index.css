@import "tailwindcss";

/* Base Typography: Apply <PERSON><PERSON><PERSON> to everything by default */
html, body {
  font-family: 'Montserrat', sans-serif;
}

/* Specific class for Playfair Display for headings where needed */
.font-playfair {
  font-family: 'Playfair Display', serif;
}

/* Custom Swiper Styles */
/* Style for the navigation arrows */
.swiper-button-prev,
.swiper-button-next {
  color: white !important;
  /* Make arrows larger */
  width: 56px !important;
  height: 56px !important;
  border-radius: 50%;
  transition: background-color 0.3s ease, transform 0.2s ease;
  /* Center the arrow icon better */
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
  transform: scale(1.05); /* Slight scale on hover */
}

/* Remove default Swiper outlines if they appear */
.swiper-button-prev:focus,
.swiper-button-next:focus {
  outline: none;
}

/* Position of arrows - might need slight adjustment if icons appear off-center */
.swiper-button-prev {
  left: 20px !important;
}
.swiper-button-next {
  right: 20px !important;
}

/* Style for the pagination dots */
.swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.6) !important;
  opacity: 1 !important;
  width: 10px !important;
  height: 10px !important;
  margin: 0 6px !important;
  transition: all 0.3s ease;
}

/* Style for the active pagination dot */
.swiper-pagination-bullet-active {
  background: white !important;
  width: 14px !important;
  height: 14px !important;
}

/* Animation classes for content inside active slide */
/* By default, elements are slightly transparent and moved up */
.swiper-slide .hero-content > * {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  transition-delay: 0.2s; /* Delay for a staggered effect */
}

/* When the slide is active, make elements fully visible and in place */
.swiper-slide-active .hero-content > *:nth-child(1) { /* Title */
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

.swiper-slide-active .hero-content > *:nth-child(2) { /* Subtitle */
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.5s;
}

.swiper-slide-active .hero-content > *:nth-child(3) { /* Button */
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.7s;
}