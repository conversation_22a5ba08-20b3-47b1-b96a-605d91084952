import React, { useState, useEffect } from 'react';
import { Check, AlertTriangle } from 'lucide-react';

const SlideModal = ({ isOpen, onClose, onSave, slide }) => {
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    imageData: '',
    buttonText: 'Shop Now',
    buttonLink: '/products',
  });
  const [preview, setPreview] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Reset state when modal opens for a new slide or a different slide
    if (isOpen) {
      if (slide) {
        setFormData({
          title: slide.title,
          subtitle: slide.subtitle || '',
          imageData: slide.imageData,
          buttonText: slide.buttonText || 'Shop Now',
          buttonLink: slide.buttonLink || '/products',
        });
        setPreview(slide.imageData);
      } else {
        setFormData({ title: '', subtitle: '', imageData: '', buttonText: 'Shop Now', buttonLink: '/products' });
        setPreview('');
      }
      setError('');
      setLoading(false);
    }
  }, [slide, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Basic size check (e.g., 10MB) to prevent huge uploads
      if (file.size > 10 * 1024 * 1024) {
        setError('Image is too large. Please select a file under 10MB.');
        return;
      }
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        setFormData(prev => ({ ...prev, imageData: reader.result }));
        setPreview(reader.result);
        setError('');
      };
      reader.onerror = () => {
        setError('Failed to read file.');
      };
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // VALIDATION LOGIC
    if (!formData.title) {
      setError('Title is a required field.');
      return;
    }
    if (!formData.imageData) {
      setError('An image is required. Please upload one.');
      return;
    }

    setLoading(true);
    setError('');
    const result = await onSave(formData); // Call the function passed from parent
    setLoading(false);
    
    if (!result.success) {
      setError(result.message || 'An unknown error occurred.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit} className="p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">{slide ? 'Edit Slide' : 'Create New Slide'}</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Slide Image <span className="text-red-500">*</span></label>
            {preview ? <img src={preview} alt="Preview" className="w-full h-40 object-cover rounded-lg mb-2 bg-gray-100" /> : <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">Image Preview</div>}
            <input type="file" onChange={handleImageChange} accept="image/*" className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"/>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2"><label className="block text-sm font-medium">Title <span className="text-red-500">*</span></label><input type="text" name="title" value={formData.title} onChange={handleChange} className="w-full p-2 border rounded-md" /></div>
            <div className="md:col-span-2"><label className="block text-sm font-medium">Subtitle</label><input type="text" name="subtitle" value={formData.subtitle} onChange={handleChange} className="w-full p-2 border rounded-md" /></div>
            <div><label className="block text-sm font-medium">Button Text</label><input type="text" name="buttonText" value={formData.buttonText} onChange={handleChange} className="w-full p-2 border rounded-md" /></div>
            <div><label className="block text-sm font-medium">Button Link</label><input type="text" name="buttonLink" value={formData.buttonLink} onChange={handleChange} className="w-full p-2 border rounded-md" /></div>
          </div>
          
          {error && (
            <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md flex items-center gap-2">
              <AlertTriangle size={18} /> {error}
            </div>
          )}
          
          <div className="flex justify-end gap-3 mt-8">
            <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 rounded-lg hover:bg-gray-300" disabled={loading}>Cancel</button>
            <button type="submit" className="py-2 px-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center gap-2" disabled={loading}>
              {loading ? 'Saving...' : 'Save Slide'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SlideModal;