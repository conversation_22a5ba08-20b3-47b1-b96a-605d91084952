import React from 'react';
import { Link } from 'react-router-dom';
import Slider from 'react-slick';

// Import slick-carousel styles
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
// Import our custom styles for the slider animations
import './Slider.css';

// This component receives the array of slides as a prop
const HeroSlider = ({ slides }) => {
  // All slider-specific settings are now encapsulated here
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    fade: true,
    cssEase: 'linear',
    pauseOnHover: true,
    appendDots: dots => (
      <div style={{ bottom: '25px' }}>
        <ul style={{ margin: '0px' }}> {dots} </ul>
      </div>
    ),
  };

  return (
    // The container with overflow-hidden remains here
    <div className="mb-12 relative overflow-x-hidden">
      <Slider {...sliderSettings}>
        {slides.map(slide => (
          <div key={slide._id} className="relative h-[650px]">
            <img src={slide.imageData} alt={slide.title} className="w-full h-full object-cover" />
            <div className="absolute inset-0 bg-black/40" />
            <div className="absolute inset-0 container mx-auto px-4 flex flex-col items-start justify-center text-white">
              <div className="max-w-md text-left">
                <h1 className="text-4xl md:text-6xl font-extrabold mb-4 slide-up">{slide.title}</h1>
                <p className="text-lg md:text-xl mb-8 slide-up-delay-1">{slide.subtitle}</p>
                <Link
                  to={slide.buttonLink}
                  className="bg-indigo-600 text-white py-3 px-8 rounded-full text-lg font-semibold hover:bg-indigo-700 transition-all duration-300 transform hover:scale-105 slide-up-delay-2"
                >
                  {slide.buttonText}
                </Link>
              </div>
            </div>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default HeroSlider;