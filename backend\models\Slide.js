import mongoose from 'mongoose';

const slideSchema = new mongoose.Schema({
  title: { type: String, required: true },
  subtitle: { type: String },
  imageData: { type: String, required: true }, // Base64 image data
  buttonText: { type: String, default: 'Shop Now' },
  buttonLink: { type: String, default: '/products' }, // Link for the CTA button
  order: { type: Number, default: 0 }, // To control slide order
  isActive: { type: Boolean, default: true }, // To show/hide slides
}, { timestamps: true });

const Slide = mongoose.model('Slide', slideSchema);
export default Slide;