{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "multer": "^2.0.1", "nodemailer": "^7.0.4", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}