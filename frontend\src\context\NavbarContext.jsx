import React from 'react';
import { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';

// 1. Create the context with a default value (can be null)
const NavbarContext = createContext(null);

// 2. Create the custom hook for consuming the context
export const useNavbar = () => {
  return useContext(NavbarContext);
};

// 3. Create the Provider component
export const NavbarProvider = ({ children }) => {
  const [navLinks, setNavLinks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Function to fetch the links from the API
  const fetchNavLinks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/navlinks');
      setNavLinks(response.data);
    } catch (error) {
      console.error('Failed to fetch nav links:', error);
      // In case of an error, you might want to set links to a default or empty array
      setNavLinks([]); 
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNavLinks();
  }, []);

  // The value that will be provided to all children
  const value = {
    navLinks,
    loading,
    fetchNavLinks, // You can also provide the refetch function if needed elsewhere
  };

  return (
    <NavbarContext.Provider value={value}>
      {children}
    </NavbarContext.Provider>
  );
};