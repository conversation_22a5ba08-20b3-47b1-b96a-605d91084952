import Order from '../models/Order.js';
import Product from '../models/Product.js';

// @desc    Create a new order
// @route   POST /api/orders
// @access  Public
export const createOrder = async (req, res) => {
    try {
        const { productId, productName, productImage, totalPrice, customerInfo } = req.body;

        if (!productId || !customerInfo) {
            return res.status(400).json({ message: 'Missing product or customer information.' });
        }

        const productExists = await Product.findById(productId);
        if (!productExists) {
            return res.status(404).json({ message: 'Product not found.' });
        }

        const order = new Order({
            product: productId,
            productName,
            productImage,
            totalPrice,
            customerInfo,
            status: 'Under Process',
        });

        const createdOrder = await order.save();
        res.status(201).json(createdOrder);
    } catch (error) {
        res.status(500).json({ message: 'Server error creating order.', error: error.message });
    }
};

// @desc    Get all orders for the admin dashboard
// @route   GET /api/orders
// @access  Private/Admin
export const getAllOrders = async (req, res) => {
    try {
        const orders = await Order.find({}).sort({ createdAt: -1 });
        res.json(orders);
    } catch (error) {
        res.status(500).json({ message: 'Server error fetching orders.' });
    }
};

// @desc    Update an order's status to 'Confirmed'
// @route   PATCH /api/orders/:id/confirm
// @access  Private/Admin
export const confirmOrder = async (req, res) => {
    try {
        const order = await Order.findById(req.params.id);

        if (order) {
            order.status = 'Confirmed';
            const updatedOrder = await order.save();
            res.json(updatedOrder);
        } else {
            res.status(404).json({ message: 'Order not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Server error confirming order.' });
    }
};