import Slide from '../models/Slide.js';

// @desc    Get all ACTIVE slides, sorted by order
// @route   GET /api/slides
export const getActiveSlides = async (req, res) => {
  try {
    const slides = await Slide.find({ isActive: true }).sort({ order: 'asc' });
    res.json(slides);
  } catch (error) {
    res.status(500).json({ message: 'Server Error' });
  }
};

// @desc    Get ALL slides for the admin panel
// @route   GET /api/slides/all
export const getAllSlidesForAdmin = async (req, res) => {
  try {
    const slides = await Slide.find({}).sort({ order: 'asc' });
    res.json(slides);
  } catch (error) {
    res.status(500).json({ message: 'Server Error' });
  }
};

// @desc    Create a new slide
// @route   POST /api/slides
export const createSlide = async (req, res) => {
  try {
    const slide = new Slide(req.body);
    const createdSlide = await slide.save();
    res.status(201).json(createdSlide);
  } catch (error) {
    res.status(400).json({ message: 'Invalid slide data' });
  }
};

// @desc    Update a slide
// @route   PATCH /api/slides/:id
export const updateSlide = async (req, res) => {
  try {
    const slide = await Slide.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!slide) return res.status(404).json({ message: 'Slide not found' });
    res.json(slide);
  } catch (error) {
    res.status(400).json({ message: 'Invalid slide data' });
  }
};

// @desc    Delete a slide
// @route   DELETE /api/slides/:id
export const deleteSlide = async (req, res) => {
  try {
    const slide = await Slide.findByIdAndDelete(req.params.id);
    if (!slide) return res.status(404).json({ message: 'Slide not found' });
    res.json({ message: 'Slide removed' });
  } catch (error) {
    res.status(500).json({ message: 'Server Error' });
  }
};