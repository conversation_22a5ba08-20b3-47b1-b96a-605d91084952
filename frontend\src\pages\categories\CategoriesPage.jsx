import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ArrowRight,
    ShoppingBag,
    Tag,
    Box,
    Search,
    Filter,
    Grid,
    List,
    SortAsc,
    SortDesc,
    Eye,
    Package,
    X,
    ChevronDown,
    Sparkles,
    TrendingUp,
    Star
} from 'lucide-react';
import ProductCard from '../Product/components/product_Card/ProductCard';

// Product Preview Modal Component
const ProductPreviewModal = ({ isOpen, onClose, category, products, loading: productsLoading }) => {
    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
                <motion.div
                    initial={{ opacity: 0, scale: 0.9, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.9, y: 20 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
                >
                    {/* Modal Header */}
                    <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex justify-between items-center z-10">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900">
                                {category?.name} Products
                            </h2>
                            <p className="text-gray-600 mt-1">
                                {products?.length || 0} products available
                            </p>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                        >
                            <X size={24} className="text-gray-600" />
                        </button>
                    </div>

                    {/* Modal Content */}
                    <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                        {productsLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                            </div>
                        ) : products && products.length > 0 ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                {products.map((product) => (
                                    <ProductCard key={product._id} product={product} />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    No Products Found
                                </h3>
                                <p className="text-gray-600">
                                    This category doesn't have any products yet.
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Modal Footer */}
                    <div className="sticky bottom-0 bg-gray-50 border-t border-gray-200 p-6">
                        <div className="flex justify-between items-center">
                            <Link
                                to={`/products?category=${category?._id}`}
                                className="text-indigo-600 hover:text-indigo-700 font-medium flex items-center gap-2"
                                onClick={onClose}
                            >
                                View All Products
                                <ArrowRight size={16} />
                            </Link>
                            <button
                                onClick={onClose}
                                className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </motion.div>
            </div>
        </AnimatePresence>
    );
};

const CategoriesPage = () => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [featuredCategories, setFeaturedCategories] = useState([]);
    const [regularCategories, setRegularCategories] = useState([]);

    // New state for enhanced functionality
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [viewMode, setViewMode] = useState('grid');
    const [selectedCategory, setSelectedCategory] = useState(null);
    const [categoryProducts, setCategoryProducts] = useState([]);
    const [productsLoading, setProductsLoading] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [hoveredCategory, setHoveredCategory] = useState(null);
    const [previewProducts, setPreviewProducts] = useState([]);

    // Fetch categories on component mount
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                const response = await axios.get('http://localhost:5000/api/categories?displayInCategoryPage=true');
                const displayedCategories = response.data;

                // Split categories into featured and regular
                const featured = displayedCategories
                    .filter(cat => cat.featuredOrder > 0)
                    .sort((a, b) => a.featuredOrder - b.featuredOrder);
                const regular = displayedCategories
                    .filter(cat => !cat.featuredOrder || cat.featuredOrder <= 0);

                setFeaturedCategories(featured);
                setRegularCategories(regular);
                setCategories(displayedCategories);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching categories:', err);
                setError('Failed to load categories. Please try again later.');
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    // Fetch products for a specific category
    const fetchCategoryProducts = async (categoryId) => {
        setProductsLoading(true);
        try {
            const response = await axios.get(`http://localhost:5000/api/products?categoryId=${categoryId}`);
            setCategoryProducts(response.data);
        } catch (err) {
            console.error('Error fetching category products:', err);
            setCategoryProducts([]);
        } finally {
            setProductsLoading(false);
        }
    };

    // Handle category hover for preview
    const handleCategoryHover = async (category) => {
        if (hoveredCategory?._id === category._id) return;

        setHoveredCategory(category);
        try {
            const response = await axios.get(`http://localhost:5000/api/products?categoryId=${category._id}`);
            setPreviewProducts(response.data.slice(0, 3)); // Show only first 3 products
        } catch (err) {
            console.error('Error fetching preview products:', err);
            setPreviewProducts([]);
        }
    };

    // Handle opening product modal
    const handleViewProducts = async (category) => {
        setSelectedCategory(category);
        setIsModalOpen(true);
        await fetchCategoryProducts(category._id);
    };

    // Filter and sort categories
    const filteredAndSortedCategories = useMemo(() => {
        let filtered = [...categories];

        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(category =>
                category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name-desc':
                    return b.name.localeCompare(a.name);
                default:
                    return 0;
            }
        });

        return filtered;
    }, [categories, searchTerm, sortBy]);

    // Split filtered categories into featured and regular
    const filteredFeatured = useMemo(() => {
        return filteredAndSortedCategories
            .filter(cat => cat.featuredOrder > 0)
            .sort((a, b) => a.featuredOrder - b.featuredOrder);
    }, [filteredAndSortedCategories]);

    const filteredRegular = useMemo(() => {
        return filteredAndSortedCategories
            .filter(cat => !cat.featuredOrder || cat.featuredOrder <= 0);
    }, [filteredAndSortedCategories]);

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.5
            }
        }
    };

    const cardVariants = {
        hidden: { scale: 0.9, opacity: 0 },
        visible: {
            scale: 1,
            opacity: 1,
            transition: {
                duration: 0.4
            }
        },
        hover: {
            scale: 1.02,
            y: -8,
            transition: {
                duration: 0.3
            }
        }
    };

    // Loading state
    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full mx-auto mb-4"
                    />
                    <motion.p
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-gray-600 text-lg"
                    >
                        Loading amazing categories...
                    </motion.p>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center max-w-md mx-auto p-8"
                >
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Oops! Something went wrong</h2>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                    >
                        Try Again
                    </button>
                </motion.div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-indigo-50">
            {/* Enhanced Hero Section */}
            <div className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white py-20 sm:py-32 overflow-hidden">
                {/* Background decorations */}
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute top-0 left-0 w-full h-full">
                    <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                    <div className="absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
                    <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-3xl"></div>
                </div>

                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="max-w-4xl"
                    >
                        <div className="flex items-center gap-3 mb-6">
                            <Sparkles className="w-8 h-8 text-yellow-300" />
                            <span className="text-yellow-300 font-semibold text-lg">Discover Amazing Products</span>
                        </div>
                        <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
                            Explore Our
                            <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
                                Categories
                            </span>
                        </h1>
                        <p className="text-xl sm:text-2xl text-indigo-100 mb-8 leading-relaxed">
                            Discover our carefully curated collection of products across various categories.
                            Find exactly what you're looking for with our intuitive browsing experience.
                        </p>
                        <div className="flex flex-wrap gap-4">
                            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                                <TrendingUp className="w-5 h-5" />
                                <span>Trending Categories</span>
                            </div>
                            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                                <Star className="w-5 h-5" />
                                <span>Premium Quality</span>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8"
                >
                    <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
                        {/* Search Bar */}
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Search categories..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
                            />
                        </div>

                        {/* Filter Controls */}
                        <div className="flex flex-wrap gap-4 items-center">
                            {/* Sort Dropdown */}
                            <div className="relative">
                                <select
                                    value={sortBy}
                                    onChange={(e) => setSortBy(e.target.value)}
                                    className="appearance-none bg-white border border-gray-200 rounded-xl px-4 py-3 pr-10 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
                                >
                                    <option value="name">Sort A-Z</option>
                                    <option value="name-desc">Sort Z-A</option>
                                </select>
                                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
                            </div>

                            {/* View Mode Toggle */}
                            <div className="flex bg-gray-100 rounded-xl p-1">
                                <button
                                    onClick={() => setViewMode('grid')}
                                    className={`p-2 rounded-lg transition-all duration-300 ${
                                        viewMode === 'grid'
                                            ? 'bg-white shadow-sm text-indigo-600'
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    <Grid className="w-5 h-5" />
                                </button>
                                <button
                                    onClick={() => setViewMode('list')}
                                    className={`p-2 rounded-lg transition-all duration-300 ${
                                        viewMode === 'list'
                                            ? 'bg-white shadow-sm text-indigo-600'
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    <List className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Results Summary */}
                    <div className="mt-4 pt-4 border-t border-gray-100">
                        <p className="text-gray-600">
                            Showing <span className="font-semibold text-indigo-600">{filteredAndSortedCategories.length}</span>
                            {searchTerm && (
                                <span> results for "<span className="font-semibold">{searchTerm}</span>"</span>
                            )}
                        </p>
                    </div>
                </motion.div>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
                {/* Featured Categories */}
                {filteredFeatured.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="mb-20"
                    >
                        <div className="flex items-center gap-3 mb-8">
                            <Star className="w-8 h-8 text-yellow-500" />
                            <h2 className="text-3xl font-bold text-gray-900">Featured Categories</h2>
                        </div>
                        <motion.div
                            variants={containerVariants}
                            initial="hidden"
                            animate="visible"
                            className={`grid gap-8 ${
                                viewMode === 'grid'
                                    ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                                    : 'grid-cols-1'
                            }`}
                        >
                            {filteredFeatured.map((category) => (
                                <motion.div
                                    key={category._id}
                                    variants={cardVariants}
                                    whileHover="hover"
                                    onHoverStart={() => handleCategoryHover(category)}
                                    className="group relative"
                                >
                                    <div className="bg-white rounded-3xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-500">
                                        {/* Category Image */}
                                        <div className="relative">
                                            {category.imageUrl ? (
                                                <div className="aspect-[16/9] overflow-hidden">
                                                    <img
                                                        src={category.imageUrl}
                                                        alt={category.name}
                                                        className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"
                                                    />
                                                </div>
                                            ) : (
                                                <div className="aspect-[16/9] bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 flex items-center justify-center">
                                                    <ShoppingBag className="w-20 h-20 text-indigo-400" />
                                                </div>
                                            )}

                                            {/* Overlay with action buttons */}
                                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                                                <div className="flex gap-3 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                                    <Link to={`/products?category=${category._id}`}>
                                                        <button className="bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-full hover:bg-white transition-all duration-300 flex items-center gap-2">
                                                            <Eye className="w-4 h-4" />
                                                            View All
                                                        </button>
                                                    </Link>
                                                    <button
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            handleViewProducts(category);
                                                        }}
                                                        className="bg-indigo-600 text-white px-4 py-2 rounded-full hover:bg-indigo-700 transition-all duration-300 flex items-center gap-2"
                                                    >
                                                        <Package className="w-4 h-4" />
                                                        Quick View
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Category Content */}
                                        <div className="p-8">
                                            <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors duration-300">
                                                {category.name}
                                            </h3>
                                            {category.description && (
                                                <p className="text-gray-600 mb-6 leading-relaxed">
                                                    {category.description}
                                                </p>
                                            )}

                                            {/* Product Preview */}
                                            {hoveredCategory?._id === category._id && previewProducts.length > 0 && (
                                                <motion.div
                                                    initial={{ opacity: 0, height: 0 }}
                                                    animate={{ opacity: 1, height: 'auto' }}
                                                    className="mb-6 border-t pt-4"
                                                >
                                                    <p className="text-sm text-gray-500 mb-3">Sample Products:</p>
                                                    <div className="flex gap-2">
                                                        {previewProducts.slice(0, 3).map((product) => (
                                                            <div key={product._id} className="flex-1">
                                                                <img
                                                                    src={product.images?.[0] || product.imageData || 'https://via.placeholder.com/100'}
                                                                    alt={product.name}
                                                                    className="w-full h-16 object-cover rounded-lg"
                                                                />
                                                                <p className="text-xs text-gray-600 mt-1 truncate">{product.name}</p>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </motion.div>
                                            )}

                                            <Link to={`/products?category=${category._id}`}>
                                                <div className="flex items-center justify-between text-indigo-600 font-semibold hover:text-indigo-700 transition-colors">
                                                    <span>Explore Products</span>
                                                    <ArrowRight className="w-5 h-5 group-hover:translate-x-2 transition-transform duration-300" />
                                                </div>
                                            </Link>
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>
                    </motion.div>
                )}

                {/* All Categories */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                >
                    <div className="flex items-center gap-3 mb-8">
                        <Tag className="w-8 h-8 text-indigo-600" />
                        <h2 className="text-3xl font-bold text-gray-900">
                            {filteredFeatured.length > 0 ? 'More Categories' : 'All Categories'}
                        </h2>
                    </div>

                    {filteredRegular.length > 0 ? (
                        <motion.div
                            variants={containerVariants}
                            initial="hidden"
                            animate="visible"
                            className={`grid gap-6 ${
                                viewMode === 'grid'
                                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                                    : 'grid-cols-1 max-w-4xl'
                            }`}
                        >
                            {filteredRegular.map((category) => (
                                <motion.div
                                    key={category._id}
                                    variants={cardVariants}
                                    whileHover="hover"
                                    onHoverStart={() => handleCategoryHover(category)}
                                    className="group"
                                >
                                    {viewMode === 'grid' ? (
                                        // Grid View Card
                                        <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 overflow-hidden">
                                            <div className="relative">
                                                {category.imageUrl ? (
                                                    <div className="aspect-[4/3] overflow-hidden">
                                                        <img
                                                            src={category.imageUrl}
                                                            alt={category.name}
                                                            className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"
                                                        />
                                                    </div>
                                                ) : (
                                                    <div className="aspect-[4/3] bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center">
                                                        <Tag className="w-12 h-12 text-indigo-400" />
                                                    </div>
                                                )}

                                                {/* Quick View Button */}
                                                <button
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handleViewProducts(category);
                                                    }}
                                                    className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-gray-700 p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:scale-110"
                                                    title="Quick view products"
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </button>
                                            </div>

                                            <div className="p-6">
                                                <div className="flex items-start justify-between mb-3">
                                                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">
                                                        {category.name}
                                                    </h3>
                                                    <Link to={`/products?category=${category._id}`}>
                                                        <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-300" />
                                                    </Link>
                                                </div>
                                                {category.description && (
                                                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                                                        {category.description}
                                                    </p>
                                                )}

                                                {/* Product Preview for Grid */}
                                                {hoveredCategory?._id === category._id && previewProducts.length > 0 && (
                                                    <motion.div
                                                        initial={{ opacity: 0, height: 0 }}
                                                        animate={{ opacity: 1, height: 'auto' }}
                                                        className="border-t pt-3 mt-3"
                                                    >
                                                        <p className="text-xs text-gray-500 mb-2">Sample Products:</p>
                                                        <div className="flex gap-1">
                                                            {previewProducts.slice(0, 3).map((product) => (
                                                                <div key={product._id} className="flex-1">
                                                                    <img
                                                                        src={product.images?.[0] || product.imageData || 'https://via.placeholder.com/60'}
                                                                        alt={product.name}
                                                                        className="w-full h-12 object-cover rounded"
                                                                    />
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </motion.div>
                                                )}

                                                <Link to={`/products?category=${category._id}`}>
                                                    <button className="w-full mt-4 bg-gray-50 hover:bg-indigo-50 text-gray-700 hover:text-indigo-600 py-2 rounded-lg transition-all duration-300 font-medium">
                                                        View Products
                                                    </button>
                                                </Link>
                                            </div>
                                        </div>
                                    ) : (
                                        // List View Card
                                        <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 p-6">
                                            <div className="flex items-center gap-6">
                                                <div className="flex-shrink-0">
                                                    {category.imageUrl ? (
                                                        <img
                                                            src={category.imageUrl}
                                                            alt={category.name}
                                                            className="w-20 h-20 object-cover rounded-xl"
                                                        />
                                                    ) : (
                                                        <div className="w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center">
                                                            <Tag className="w-8 h-8 text-indigo-400" />
                                                        </div>
                                                    )}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors duration-300">
                                                        {category.name}
                                                    </h3>
                                                    {category.description && (
                                                        <p className="text-gray-600 mb-3">
                                                            {category.description}
                                                        </p>
                                                    )}

                                                    <div className="flex gap-3">
                                                        <Link to={`/products?category=${category._id}`}>
                                                            <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors duration-300">
                                                                View Products
                                                            </button>
                                                        </Link>
                                                        <button
                                                            onClick={() => handleViewProducts(category)}
                                                            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-300 flex items-center gap-2"
                                                        >
                                                            <Eye className="w-4 h-4" />
                                                            Quick View
                                                        </button>
                                                    </div>
                                                </div>

                                                <div className="flex-shrink-0">
                                                    <ArrowRight className="w-6 h-6 text-gray-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-300" />
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </motion.div>
                            ))}
                        </motion.div>
                    ) : (
                        // No Categories Found
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="text-center py-16"
                        >
                            <Box className="w-20 h-20 text-gray-400 mx-auto mb-6" />
                            <h3 className="text-2xl font-bold text-gray-900 mb-3">
                                {searchTerm ? 'No Categories Found' : 'No Categories Available'}
                            </h3>
                            <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                {searchTerm
                                    ? `No categories match your search for "${searchTerm}". Try adjusting your search terms.`
                                    : 'Please check back later for our category listings.'
                                }
                            </p>
                            {searchTerm && (
                                <button
                                    onClick={() => setSearchTerm('')}
                                    className="bg-indigo-600 text-white px-6 py-3 rounded-xl hover:bg-indigo-700 transition-colors duration-300"
                                >
                                    Clear Search
                                </button>
                            )}
                        </motion.div>
                    )}
                </motion.div>
            </div>

            {/* Product Preview Modal */}
            <ProductPreviewModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                category={selectedCategory}
                products={categoryProducts}
                loading={productsLoading}
            />
        </div>
    );
};

export default CategoriesPage;