import mongoose from 'mongoose';

const navLinkSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  // Path is no longer strictly required, as dropdowns won't have one
  path: {
    type: String,
    // A dropdown container might have a path like '#' or null
  },
  parentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NavLink',
    default: null,
  },
  // This is the new, important field
  isDropdown: {
    type: Boolean,
    default: false,
  },
  order: { // Optional: For drag-and-drop in the future
    type: Number,
    default: 0,
  }
}, { timestamps: true });

const NavLink = mongoose.model('NavLink', navLinkSchema);

export default NavLink;