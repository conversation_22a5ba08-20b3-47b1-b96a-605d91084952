/* Animation for the slider text */
.slick-active .slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.slick-active .slide-up-delay-1 {
  animation: slideUp 0.8s 0.2s ease-out forwards;
  opacity: 0;
}

.slick-active .slide-up-delay-2 {
  animation: slideUp 0.8s 0.4s ease-out forwards;
  opacity: 0;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style the slick dots for a cleaner look */
.slick-dots li button:before {
  font-size: 12px;
  color: white;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.slick-dots li.slick-active button:before {
  opacity: 1;
  color: #6366f1; /* Indigo */
}