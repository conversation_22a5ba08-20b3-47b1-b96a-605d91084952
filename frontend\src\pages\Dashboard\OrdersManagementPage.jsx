import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Check, RefreshCw } from 'lucide-react';

const OrdersManagementPage = () => {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeFilter, setActiveFilter] = useState('all');

    const fetchOrders = async () => {
        setLoading(true);
        setError('');
        try {
            const response = await axios.get('http://localhost:5000/api/orders');
            setOrders(response.data);
        } catch (err) {
            setError('Failed to fetch orders. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchOrders();
    }, []);

    const handleConfirmOrder = async (orderId) => {
        try {
            await axios.patch(`http://localhost:5000/api/orders/${orderId}/confirm`);
            // Refetch orders to get the updated list
            fetchOrders(); 
        } catch (err) {
            setError('Failed to confirm order. Please try again.');
        }
    };

    const filteredOrders = orders.filter(order => {
        if (activeFilter === 'all') return true;
        // Match status like 'Under Process' to 'under-process'
        return order.status.toLowerCase().replace(' ', '-') === activeFilter;
    });

    return (
        <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
            <h1 className="text-3xl font-bold text-gray-800 mb-6">Order Management</h1>

            {error && <div className="bg-red-100 text-red-700 p-3 rounded-lg mb-4">{error}</div>}

            <div className="bg-white rounded-xl shadow-lg">
                <div className="p-4 border-b flex justify-between items-center">
                    <div className="flex gap-2">
                        <button onClick={() => setActiveFilter('all')} className={`px-4 py-2 rounded-lg text-sm font-semibold ${activeFilter === 'all' ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}>All Orders</button>
                        <button onClick={() => setActiveFilter('under-process')} className={`px-4 py-2 rounded-lg text-sm font-semibold ${activeFilter === 'under-process' ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-700'}`}>Under Process</button>
                        <button onClick={() => setActiveFilter('confirmed')} className={`px-4 py-2 rounded-lg text-sm font-semibold ${activeFilter === 'confirmed' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'}`}>Confirmed</button>
                    </div>
                    <button onClick={fetchOrders} className="p-2 text-gray-500 hover:text-indigo-600"><RefreshCw size={20} className={loading ? 'animate-spin' : ''} /></button>
                </div>

                <div className="overflow-x-auto">
                    {loading ? <div className="p-8 text-center">Loading orders...</div> : (
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer & Address</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Action</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y">
                                {filteredOrders.map(order => (
                                    <tr key={order._id}>
                                        <td className="px-6 py-4"><div className="flex items-center gap-3"><img src={order.productImage || 'https://via.placeholder.com/100'} alt={order.productName} className="w-12 h-12 object-cover rounded-md bg-gray-100 flex-shrink-0"/><span className="font-medium text-gray-900">{order.productName}</span></div></td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <div className="font-semibold text-gray-800">{order.customerInfo.firstName} {order.customerInfo.lastName}</div>
                                            <div className="text-xs">{order.customerInfo.wilaya}, {order.customerInfo.address}</div>
                                            <div className="text-xs text-indigo-600 font-medium">{order.customerInfo.phone1}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold">${order.totalPrice.toFixed(2)}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${order.status === 'Confirmed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                                {order.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right">
                                            {order.status === 'Under Process' && (
                                                <button onClick={() => handleConfirmOrder(order._id)} className="bg-green-500 text-white px-3 py-1 rounded-md text-xs font-bold hover:bg-green-600 flex items-center gap-1 transition-colors">
                                                    <Check size={14} /> Confirm
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    )}
                     {filteredOrders.length === 0 && !loading && <p className="text-center text-gray-500 py-8">No orders found for this filter.</p>}
                </div>
            </div>
        </div>
    );
};

export default OrdersManagementPage;